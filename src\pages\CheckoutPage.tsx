import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { type RootState } from '../store';
import { placeOrder } from '../store/orderSlice';
import { clearCart } from '../store/cartSlice';
import { updateProductStock } from '../store/productSlice';
import type { CartItem, Order } from '../types';

const CheckoutPage: React.FC = () => {
  const [filteredCartItems, setFilteredCartItems] = useState<CartItem[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const cartItems = useSelector((state: RootState) => state.cart.items);
  const products = useSelector((state: RootState) => state.product.products);
  const currentUser = useSelector((state: RootState) => state.user.currentUser);

  useEffect(() => {
    if (!currentUser) {
      navigate('/login');
      return;
    }

    // Filter out items that are out of stock or exceed available stock
    const validCartItems = cartItems.filter((item) => {
      const productInStock = products.find((p) => p.id === item.product.id);
      return productInStock && productInStock.stock > 0 && item.quantity <= productInStock.stock;
    });

    setFilteredCartItems(validCartItems);
  }, [currentUser, cartItems, products, navigate]);

  const handleConfirmOrder = async () => {
    if (!currentUser) {
      navigate('/login');
      return;
    }

    if (filteredCartItems.length === 0) {
      navigate('/cart');
      return;
    }

    setIsProcessing(true);

    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Prepare items for order and update product stock
    const orderItems = filteredCartItems.map((item) => {
      const productDetails = products.find(p => p.id === item.product.id);
      if (!productDetails) {
        // This case should ideally not happen if filteredCartItems is correctly derived
        // but as a safeguard, return the item as is or throw an error
        return item;
      }
      // Dispatch action to update stock for each product
      dispatch(updateProductStock({ productId: item.product.id, quantitySold: item.quantity }));
      return {
        product: {
          id: productDetails.id,
          name: productDetails.name,
          price: productDetails.price,
          category: productDetails.category,
          rating: productDetails.rating,
          stock: productDetails.stock - item.quantity, // Reflect new stock in order item
        },
        quantity: item.quantity,
      };
    });

    const newOrder: Order = {
      id: Date.now().toString(),
      userId: currentUser.id,
      items: orderItems,
      total: filteredCartItems.reduce((sum, item) => sum + item.product.price * item.quantity, 0),
      orderDate: new Date().toISOString(),
    };

    dispatch(placeOrder(newOrder));
    dispatch(clearCart());
    setIsProcessing(false);
    navigate('/order-history');
  };

  const total = filteredCartItems.reduce((sum, item) => sum + item.product.price * item.quantity, 0);

  return (
    <div className="container my-4">
      <div className="row">
        <div className="col-12">
          <nav aria-label="breadcrumb" className="mb-4">
            <ol className="breadcrumb">
              <li className="breadcrumb-item">
                <button 
                  onClick={() => navigate('/')}
                  className="btn btn-link p-0 text-decoration-none"
                >
                  Home
                </button>
              </li>
              <li className="breadcrumb-item">
                <button 
                  onClick={() => navigate('/cart')}
                  className="btn btn-link p-0 text-decoration-none"
                >
                  Cart
                </button>
              </li>
              <li className="breadcrumb-item active" aria-current="page">
                Checkout
              </li>
            </ol>
          </nav>
          
          <h2 className="mb-4">
            <i className="bi bi-credit-card me-2"></i>
            Checkout Summary
          </h2>
        </div>
      </div>

      {filteredCartItems.length === 0 ? (
        <div className="row">
          <div className="col-12">
            <div className="alert alert-warning" role="alert">
              <h4 className="alert-heading">
                <i className="bi bi-exclamation-triangle me-2"></i>
                No Items Available for Checkout
              </h4>
              <p className="mb-3">
                Your cart might be empty or all items are currently out of stock.
              </p>
              <hr />
              <div className="d-flex gap-2">
                <button 
                  onClick={() => navigate('/cart')}
                  className="btn btn-outline-warning"
                >
                  Back to Cart
                </button>
                <button 
                  onClick={() => navigate('/')}
                  className="btn btn-warning"
                >
                  Continue Shopping
                </button>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="row">
          <div className="col-lg-8">
            <div className="card mb-4">
              <div className="card-header">
                <h5 className="mb-0">
                  <i className="bi bi-list-check me-2"></i>
                  Order Items ({filteredCartItems.length})
                </h5>
              </div>
              <div className="card-body p-0">
                {filteredCartItems.map((item, index) => (
                  <div key={item.product.id} className={`p-4 ${index !== filteredCartItems.length - 1 ? 'border-bottom' : ''}`}>
                    <div className="row align-items-center">
                      <div className="col-md-6">
                        <h6 className="mb-1">{item.product.name}</h6>
                        <p className="text-muted mb-0">
                          <small>Category: {item.product.category}</small>
                        </p>
                      </div>
                      <div className="col-md-2 text-center">
                        <p className="mb-0">
                          <span className="fw-semibold">
                            ${item.product.price.toFixed(2)}
                          </span>
                        </p>
                        <small className="text-muted">per item</small>
                      </div>
                      <div className="col-md-2 text-center">
                        <span className="badge bg-primary rounded-pill">
                          {item.quantity}
                        </span>
                      </div>
                      <div className="col-md-2 text-end">
                        <p className="mb-0">
                          <strong className="text-success">
                            ${(item.product.price * item.quantity).toFixed(2)}
                          </strong>
                        </p>
                        <small className="text-muted">subtotal</small>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="col-lg-4">
            <div className="card sticky-top" style={{ top: '20px' }}>
              <div className="card-header">
                <h5 className="mb-0">
                  <i className="bi bi-receipt me-2"></i>
                  Order Summary
                </h5>
              </div>
              <div className="card-body">
                <div className="d-flex justify-content-between mb-3">
                  <span>Subtotal:</span>
                  <span>${total.toFixed(2)}</span>
                </div>
                <div className="d-flex justify-content-between mb-3">
                  <span>Shipping:</span>
                  <span className="text-success">FREE</span>
                </div>
                <div className="d-flex justify-content-between mb-3">
                  <span>Tax:</span>
                  <span>$0.00</span>
                </div>
                <hr />
                <div className="d-flex justify-content-between mb-4">
                  <strong className="fs-5">Total:</strong>
                  <strong className="fs-4 text-primary">
                    ${total.toFixed(2)}
                  </strong>
                </div>

                <div className="alert alert-success mb-4" role="alert">
                  <i className="bi bi-shield-check me-2"></i>
                  <small>
                    <strong>Secure Checkout:</strong> Your payment information is protected with 256-bit SSL encryption.
                  </small>
                </div>

                <div className="d-grid gap-2">
                  <button 
                    onClick={handleConfirmOrder} 
                    className="btn btn-success btn-lg"
                    disabled={isProcessing}
                  >
                    {isProcessing ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        Processing Order...
                      </>
                    ) : (
                      <>
                        <i className="bi bi-check-circle me-2"></i>
                        Confirm Order
                      </>
                    )}
                  </button>
                  <button 
                    onClick={() => navigate('/cart')}
                    className="btn btn-outline-secondary"
                    disabled={isProcessing}
                  >
                    <i className="bi bi-arrow-left me-2"></i>
                    Back to Cart
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Order Confirmation Modal would go here in a real app */}
      {isProcessing && (
        <div className="position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center" 
             style={{ backgroundColor: 'rgba(0,0,0,0.5)', zIndex: 9999 }}>
          <div className="card shadow-lg">
            <div className="card-body text-center p-5">
              <div className="spinner-border text-success mb-3" style={{ width: '3rem', height: '3rem' }} role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
              <h4>Processing Your Order...</h4>
              <p className="text-muted">Please wait while we confirm your order.</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CheckoutPage;
