import React, { useEffect, useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { type RootState, type AppDispatch } from '../store';
import { logoutUser } from '../store/userSlice';

const Header: React.FC = () => {
  const dispatch: AppDispatch = useDispatch();
  const currentUser = useSelector((state: RootState) => state.user.currentUser);
  const cartItems = useSelector((state: RootState) => state.cart.items);
  const [cartItemCount, setCartItemCount] = useState<number>(0);
  const location = useLocation();

  const handleLogout = () => {
    dispatch(logoutUser());
  };

  const isActive = (path: string) => location.pathname === path;

  useEffect(() => {
    setCartItemCount(cartItems.reduce((total, item) => total + item.quantity, 0));
  }, [cartItems]);


  return (
    <nav className="navbar bg-primary fixed-top navbar-dark">
      <div className="container-fluid">
        <Link to="/" className="navbar-brand fw-bold">
          <i className="bi bi-shop me-2"></i>
          ShopApp
        </Link>

        <button
          className="navbar-toggler border-0"
          type="button"
          data-bs-toggle="offcanvas"
          data-bs-target="#offcanvasNavbar"
          aria-controls="offcanvasNavbar"
          aria-label="Toggle navigation"
        >
          <span className="navbar-toggler-icon"></span>
        </button>

        <div className="offcanvas offcanvas-end" tabIndex={-1} id="offcanvasNavbar" aria-labelledby="offcanvasNavbarLabel">
          <div className="offcanvas-header bg-primary">
            <h5 className="offcanvas-title fw-bold" id="offcanvasNavbarLabel">
              <i className="bi bi-shop me-2"></i>
              ShopApp Menu
            </h5>
            <button
              type="button"
              className="btn-close btn-close-dark"
              data-bs-dismiss="offcanvas"
              aria-label="Close"
            ></button>
          </div>

          <div className="offcanvas-body">
            {/* User Welcome Section */}
            {currentUser && (
              <div className="rounded p-3 mb-4">
                <div className="d-flex align-items-center">
                  <div className="bg-primary rounded-circle p-2 me-3">
                    <i className="bi bi-person-fill"></i>
                  </div>
                  <div>
                    <h6 className="mb-0 fw-bold">Welcome back!</h6>
                    <small className="text-muted">{currentUser.username}</small>
                  </div>
                </div>
              </div>
            )}

            {/* Main Navigation */}
            <ul className="navbar-nav justify-content-end flex-grow-1 pe-3">
              <li className="nav-item">
                <Link
                  to="/"
                  className={`nav-link fw-medium ${isActive('/') ? 'active' : ''}`}
                  
                >
                  <i className="bi bi-grid3x3-gap me-2"></i>
                  Products
                </Link>
              </li>

              <li className="nav-item">
                <Link
                  to="/cart"
                  className={`nav-link fw-medium position-relative text-dark ${isActive('/cart') ? 'active' : ''}`}
                  
                >
                  <i className="bi bi-cart3 me-2"></i>
                  Shopping Cart
                  {cartItemCount > 0 && (
                    <span className="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                      {cartItemCount}
                      <span className="visually-hidden">items in cart</span>
                    </span>
                  )}
                </Link>
              </li>

              {currentUser ? (
                <>
                  <li className="nav-item">
                    <Link
                      to="/order-history"
                      className={`nav-link fw-medium text-dark ${isActive('/order-history') ? 'active' : ''}`}
                      
                    >
                      <i className="bi bi-clock-history me-2"></i>
                      Order History
                    </Link>
                  </li>

                  <li className="nav-item dropdown">
                    <a
                      className="nav-link text-dark dropdown-toggle fw-medium"
                      href="#"
                      role="button"
                      data-bs-toggle="dropdown"
                      aria-expanded="false"
                    >
                      <i className="bi bi-person-circle me-2"></i>
                      Account
                    </a>
                    <ul className="dropdown-menu">
                      <li>
                        <Link
                          to="/profile"
                          className="dropdown-item"
                          
                        >
                          <i className="bi bi-person me-2"></i>
                          Profile Settings
                        </Link>
                      </li>
                      <li><hr className="dropdown-divider" /></li>
                      <li>
                        <button
                          onClick={handleLogout}
                          className="dropdown-item text-danger"
                        >
                          <i className="bi bi-box-arrow-right me-2"></i>
                          Logout
                        </button>
                      </li>
                    </ul>
                  </li>
                </>
              ) : (
                <>
                  <li className="nav-item">
                    <Link
                      to="/login"
                      className={`nav-link fw-medium ${isActive('/login') ? 'active' : ''}`}
                      
                    >
                      <i className="bi bi-box-arrow-in-right me-2"></i>
                      Sign In
                    </Link>
                  </li>
                  <li className="nav-item">
                    <Link
                      to="/register"
                      className={`nav-link fw-medium ${isActive('/register') ? 'active' : ''}`}
                      
                    >
                      <i className="bi bi-person-plus me-2"></i>
                      Create Account
                    </Link>
                  </li>
                </>
              )}
            </ul>

            {/* Footer */}
            <div className="mt-auto pt-4 text-center">
              <small className="text-muted">
                <i className="bi bi-shield-check me-1"></i>
                Secure Shopping Experience
              </small>
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Header;
