import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { type RootState } from '../store';
import { setProducts } from '../store/productSlice';
import { addToCart } from '../store/cartSlice';
import type { CartItem, Product } from '../types';
import allProductsData from '../assets/products/all_products.json';

const ProductListingPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const products = useSelector((state: RootState) => state.product.products);
  const cartItems = useSelector((state: RootState) => state.cart.items);
  const currentUser = useSelector((state: RootState) => state.user.currentUser);

  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [categoryFilter, setCategoryFilter] = useState('All');
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [sortBy, setSortBy] = useState('default');
  const [message, setMessage] = useState('');

  // Load products into Redux store if not already loaded
  useEffect(() => {
    if (products.length === 0) {
      dispatch(setProducts(allProductsData as Product[]));
    }
  }, [dispatch, products.length]);

  // Redirect if no current user
  useEffect(() => {
    if (!currentUser) {
      navigate('/login');
    }
  }, [currentUser, navigate]);

  const handleAddToCart = (product: Product) => {
    if (!product) return;

    if (product.stock === 0) {
      setMessage('This product is out of stock.');
      return;
    }

    const existingItem = cartItems.find((item) => item.product.id === product.id);

    if (existingItem) {
      const newQuantity = existingItem.quantity + 1;

      if (newQuantity > product.stock) {
        setMessage(`Adding ${product.name} would exceed stock. Current in cart: ${existingItem.quantity}, Available: ${product.stock}`);
        return;
      }

      dispatch(addToCart({ product, quantity: newQuantity }));
    } else {
      const newItem: CartItem = {
        product: { ...product },
        quantity: 1,
      };
      dispatch(addToCart(newItem));
    }
    setMessage(`${product.name} added to cart!`);
  };

  useEffect(() => {
    let tempProducts = [...products];

    // Filtering by category
    if (categoryFilter !== 'All') {
      tempProducts = tempProducts.filter(
        (product) => product.category === categoryFilter
      );
    }

    // Filtering by search term
    if (searchTerm) {
      tempProducts = tempProducts.filter((product) =>
        product.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Sorting
    switch (sortBy) {
      case 'price-asc':
        tempProducts.sort((a, b) => a.price - b.price);
        break;
      case 'price-desc':
        tempProducts.sort((a, b) => b.price - a.price);
        break;
      case 'rating-desc':
        tempProducts.sort((a, b) => b.rating - a.rating);
        break;
      case 'rating-asc':
        tempProducts.sort((a, b) => a.rating - b.rating);
        break;
    }
    setFilteredProducts(tempProducts);
  }, [products, categoryFilter, sortBy, searchTerm]);

  const categories = Array.from(new Set(products.map((p) => p.category)));

  // Pagination logic
  const productsPerPage = 8;
  const indexOfLastProduct = currentPage * productsPerPage;
  const indexOfFirstProduct = indexOfLastProduct - productsPerPage;
  const currentProducts = filteredProducts.slice(
    indexOfFirstProduct,
    indexOfLastProduct
  );
  const totalPages = Math.ceil(filteredProducts.length / productsPerPage);

  const paginate = (pageNumber: number) => {
    if (pageNumber > 0 && pageNumber <= totalPages) {
      setCurrentPage(pageNumber);
    }
  };

  return (
    <div className="container my-4">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h2 className="mb-0">Products</h2>
      </div>

      <div className="row mb-4">
        <div className="col-md-4">
          <div className="card">
            <div className="card-body">
              <label htmlFor="search-bar" className="form-label fw-semibold">
                Search by Name:
              </label>
              <input
                type="text"
                id="search-bar"
                className="form-control"
                placeholder="Search products..."
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value);
                  setCurrentPage(1);
                }}
              />
            </div>
          </div>
        </div>
        <div className="col-md-4">
          <div className="card">
            <div className="card-body">
              <label htmlFor="category-filter" className="form-label fw-semibold">
                Filter by Category:
              </label>
              <select
                id="category-filter"
                value={categoryFilter}
                onChange={(e) => {
                  setCategoryFilter(e.target.value);
                  setCurrentPage(1);
                }}
                className="form-select"
              >
                <option value="All">All Categories</option>
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
        <div className="col-md-4">
          <div className="card">
            <div className="card-body">
              <label htmlFor="sort-by" className="form-label fw-semibold">
                Sort by:
              </label>
              <select
                id="sort-by"
                value={sortBy}
                onChange={(e) => {
                  setSortBy(e.target.value);
                  setCurrentPage(1);
                }}
                className="form-select"
              >
                <option value="default">Default</option>
                <option value="price-asc">Price: Low to High</option>
                <option value="price-desc">Price: High to Low</option>
                <option value="rating-desc">Rating: High to Low</option>
                <option value="rating-asc">Rating: Low to High</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {message && (
        <div
          className={`alert ${message.includes('added to cart') ? 'alert-success' : 'alert-warning'
            } alert-dismissible fade show`}
          role="alert"
          style={{
            position: 'fixed',
            top: '0',
            left: '0',
            right: '0',
            zIndex: '1050', // ensures it's above other elements
            margin: '0',
            borderRadius: '0',
          }}
        >
          {message}
          <button
            type="button"
            className="btn-close"
            onClick={() => setMessage('')}
            aria-label="Close"
          ></button>
        </div>
      )}


      {filteredProducts.length > 0 ? (
        <>
          <div className="row g-4">
            {currentProducts.map((product) => (
              <div key={product.id} className="col-sm-6 col-md-4 col-lg-3">
                <div className="card h-100 shadow-sm">
                  {/* {product.image && ( */}
                  <img
                    src={product.image}
                    alt={product.name}
                    className="card-img-top object-fit-contain"
                    style={{ height: '200px', objectFit: 'cover' }}
                  />
                  {/* )} */}
                  <div className="card-body d-flex flex-column">
                    <h5 className="card-title">{product.name}</h5>
                    <p className="card-text">
                      <span className="fw-bold text-primary fs-5">
                        ${product.price.toFixed(2)}
                      </span>
                    </p>
                    <p className="card-text">
                      <small className="text-muted">Category: {product.category}</small>
                    </p>
                    <p className="card-text">
                      <span className={`badge ${product.stock > 0 ? 'bg-success' : 'bg-danger'}`}>
                        {product.stock > 0 ? `${product.stock} in stock` : 'Out of Stock'}
                      </span>
                    </p>
                    <p className="card-text">
                      <span className="text-warning">
                        {'★'.repeat(Math.floor(product.rating))}
                        {'☆'.repeat(5 - Math.floor(product.rating))}
                      </span>
                      <small className="text-muted ms-1">({product.rating}/5)</small>
                    </p>
                    <div className="mt-auto">
                      <button
                        onClick={() => navigate(`/product/${product.id}`)}
                        className="btn btn-outline-primary m-2 w-100"
                        // disabled={product.stock === 0}
                      >
                        View Details
                      </button>
                      <button
                        onClick={() => handleAddToCart(product)}
                        className="btn btn-outline-success m-2 w-100"
                        disabled={product.stock === 0}
                      >
                        Add to Cart
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
          {totalPages > 1 && (
            <nav className="mt-4" aria-label="Page navigation">
              <ul className="pagination justify-content-center">
                <li className={`page-item ${currentPage === 1 ? 'disabled' : ''}`}>
                  <button className="page-link" onClick={() => paginate(currentPage - 1)}>
                    Previous
                  </button>
                </li>
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((pageNumber) => (
                  <li
                    key={pageNumber}
                    className={`page-item ${currentPage === pageNumber ? 'active' : ''}`}
                  >
                    <button className="page-link" onClick={() => paginate(pageNumber)}>
                      {pageNumber}
                    </button>
                  </li>
                ))}
                <li className={`page-item ${currentPage === totalPages ? 'disabled' : ''}`}>
                  <button className="page-link" onClick={() => paginate(currentPage + 1)}>
                    Next
                  </button>
                </li>
              </ul>
            </nav>
          )}
        </>
      ) : (
        <div className="row">
          <div className="col-12">
            <div className="alert alert-info text-center" role="alert">
              <h4 className="alert-heading">No Products Found</h4>
              <p className="mb-0">Try adjusting your filter or check back later for new products.</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductListingPage;
