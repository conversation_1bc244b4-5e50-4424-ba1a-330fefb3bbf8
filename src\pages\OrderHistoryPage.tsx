import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { getOrders, getCurrentUser } from '../utils/localStorage';
import type { Order } from '../types';

const OrderHistoryPage: React.FC = () => {
  const [orders, setOrders] = useState<Order[]>([]);
  const navigate = useNavigate();

  useEffect(() => {
    const user = getCurrentUser();
    if (!user) {
      navigate('/login');
      return;
    }
    const allOrders = getOrders();
    const userOrders = allOrders.filter((order) => order.userId === user.id);
    setOrders(userOrders);
  }, [navigate]);

  return (
    <div className="container py-5">
      <div className="row justify-content-center">
        <div className="col-lg-10">
          <div className="d-flex align-items-center mb-4">
            <h2 className="text-primary mb-0 me-3">Your Order History</h2>
            <span className="badge bg-secondary">{orders.length} orders</span>
          </div>
          
          {orders.length === 0 ? (
            <div className="text-center py-5">
              <div className="mb-4">
                <i className="bi bi-cart-x display-1 text-muted"></i>
              </div>
              <h4 className="text-muted mb-3">No Orders Yet</h4>
              <p className="text-muted mb-4">You haven't placed any orders yet.</p>
              <button 
                className="btn btn-primary btn-lg"
                onClick={() => navigate('/products')}
              >
                Start Shopping
              </button>
            </div>
          ) : (
            <div className="row g-4">
              {orders.map((order) => (
                <div key={order.id} className="col-12">
                  <div className="card shadow-sm border-0 h-100">
                    <div className="card-header bg-light border-0 py-3">
                      <div className="row align-items-center">
                        <div className="col-md-6">
                          <h5 className="card-title mb-0 text-primary">
                            <i className="bi bi-receipt me-2"></i>
                            Order #{order.id}
                          </h5>
                        </div>
                        <div className="col-md-6 text-md-end">
                          <small className="text-muted">
                            <i className="bi bi-calendar3 me-1"></i>
                            {new Date(order.orderDate).toLocaleDateString('en-US', {
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric',
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </small>
                        </div>
                      </div>
                    </div>
                    
                    <div className="card-body">
                      <div className="mb-3">
                        <h6 className="text-secondary mb-3">
                          <i className="bi bi-box-seam me-2"></i>
                          Items Ordered
                        </h6>
                        <div className="row g-2">
                          {order.items.map((item) => (
                            <div key={item.product.id} className="col-12">
                              <div className="d-flex justify-content-between align-items-center py-2 px-3 bg-light rounded">
                                <div className="flex-grow-1">
                                  <span className="fw-medium">{item.product.name}</span>
                                  <small className="text-muted ms-2">
                                    ${item.product.price.toFixed(2)} each
                                  </small>
                                </div>
                                <div className="text-end">
                                  <span className="badge bg-primary rounded-pill me-2">
                                    Qty: {item.quantity}
                                  </span>
                                  <span className="fw-bold text-success">
                                    ${(item.product.price * item.quantity).toFixed(2)}
                                  </span>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                    
                    <div className="card-footer bg-white border-0 pt-0">
                      <div className="d-flex justify-content-between align-items-center">
                        <div className="text-muted">
                          <small>
                            <i className="bi bi-bag-check me-1"></i>
                            {order.items.length} item{order.items.length !== 1 ? 's' : ''}
                          </small>
                        </div>
                        <div>
                          <h5 className="mb-0 text-success">
                            <i className="bi bi-currency-dollar me-1"></i>
                            Total: ${order.total.toFixed(2)}
                          </h5>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default OrderHistoryPage;