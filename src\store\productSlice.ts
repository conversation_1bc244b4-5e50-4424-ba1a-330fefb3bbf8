import { createSlice, type PayloadAction } from '@reduxjs/toolkit';
import { type Product } from '../types';

export interface ProductState {
  products: Product[];
  filters: {
    category: string;
    minPrice: number;
    maxPrice: number;
  };
  selectedProduct: Product | null;
}

const initialState: ProductState = {
  products: [],
  filters: {
    category: 'All',
    minPrice: 0,
    maxPrice: 1000, // Assuming a max price for initial filter
  },
  selectedProduct: null,
};

const productSlice = createSlice({
  name: 'product',
  initialState,
  reducers: {
    setProducts: (state, action: PayloadAction<Product[]>) => {
      state.products = action.payload;
    },
    setFilters: (state, action: PayloadAction<Partial<ProductState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    setSelectedProduct: (state, action: PayloadAction<Product | null>) => {
      state.selectedProduct = action.payload;
    },
    updateProductStock: (state, action: PayloadAction<{ productId: string; quantitySold: number }>) => {
      const { productId, quantitySold } = action.payload;
      const product = state.products.find((p) => p.id === productId);
      if (product) {
        product.stock -= quantitySold;
      }
    },
  },
});

export const { setProducts, setFilters, setSelectedProduct, updateProductStock } = productSlice.actions;
export default productSlice.reducer;
