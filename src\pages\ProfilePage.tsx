import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { getCurrentUser, setCurrentUser, clearCurrentUser } from '../utils/localStorage';
import type { User } from '../types';

const ProfilePage: React.FC = () => {
  const [currentUser, setCurrentUserState] = useState<User | null>(null);
  const [editMode, setEditMode] = useState(false);
  const [formData, setFormData] = useState<Partial<User>>({});
  const navigate = useNavigate();

  useEffect(() => {
    const user = getCurrentUser();
    if (!user) {
      navigate('/login');
      return;
    }
    setCurrentUserState(user);
    setFormData(user);
  }, [navigate]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSave = (e: React.FormEvent) => {
    e.preventDefault();
    if (currentUser) {
      const updatedUser: User = { ...currentUser, ...formData };
      setCurrentUser(updatedUser);
      setCurrentUserState(updatedUser);
      setEditMode(false);
      // Using a more user-friendly notification instead of alert
      const toast = document.createElement('div');
      toast.className = 'alert alert-success position-fixed top-0 end-0 m-3';
      toast.style.zIndex = '9999';
      toast.innerHTML = '<i class="bi bi-check-circle me-2"></i>Profile updated successfully!';
      document.body.appendChild(toast);
      setTimeout(() => document.body.removeChild(toast), 3000);
    }
  };

  const handleLogout = () => {
    clearCurrentUser();
    navigate('/login');
  };

  if (!currentUser) {
    return (
      <div className="container d-flex justify-content-center align-items-center min-vh-100">
        <div className="text-center">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-3 text-muted">Loading profile...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-5">
      <div className="row justify-content-center">
        <div className="col-lg-8 col-xl-6">
          <div className="card shadow-sm border-0">
            <div className="card-header bg-primary text-white py-4">
              <div className="d-flex align-items-center">
                <div className="bg-white bg-opacity-25 rounded-circle p-3 me-3">
                  <i className="bi bi-person-fill text-white display-6"></i>
                </div>
                <div>
                  <h2 className="mb-1 fw-bold">User Profile</h2>
                  <p className="mb-0 opacity-75">Manage your account information</p>
                </div>
              </div>
            </div>

            <div className="card-body p-4">
              {editMode ? (
                <form onSubmit={handleSave}>
                  <div className="mb-4">
                    <h5 className="text-secondary mb-3">
                      <i className="bi bi-pencil-square me-2"></i>
                      Edit Information
                    </h5>
                  </div>

                  <div className="mb-4">
                    <label htmlFor="username" className="form-label fw-medium">
                      <i className="bi bi-person me-2 text-primary"></i>
                      Username
                    </label>
                    <input
                      type="text"
                      className="form-control form-control-lg"
                      id="username"
                      name="username"
                      value={formData.username || ''}
                      onChange={handleChange}
                      placeholder="Enter your username"
                      required
                    />
                  </div>

                  <div className="mb-4">
                    <label htmlFor="email" className="form-label fw-medium">
                      <i className="bi bi-envelope me-2 text-primary"></i>
                      Email Address
                    </label>
                    <input
                      type="email"
                      className="form-control form-control-lg"
                      id="email"
                      name="email"
                      value={formData.email || ''}
                      onChange={handleChange}
                      placeholder="Enter your email address"
                      required
                    />
                  </div>

                  <div className="alert alert-info d-flex align-items-start">
                    <i className="bi bi-info-circle-fill me-2 mt-1"></i>
                    <div>
                      <strong>Password Changes:</strong> For security reasons, password updates require a separate verification process.
                    </div>
                  </div>

                  <div className="d-flex gap-2 pt-3">
                    <button type="submit" className="btn btn-success btn-lg flex-fill">
                      <i className="bi bi-check-lg me-2"></i>
                      Save Changes
                    </button>
                    <button 
                      type="button" 
                      onClick={() => setEditMode(false)} 
                      className="btn btn-outline-secondary btn-lg flex-fill"
                    >
                      <i className="bi bi-x-lg me-2"></i>
                      Cancel
                    </button>
                  </div>
                </form>
              ) : (
                <div>
                  <div className="mb-4">
                    <h5 className="text-secondary mb-4">
                      <i className="bi bi-info-circle me-2"></i>
                      Account Details
                    </h5>
                  </div>

                  <div className="row g-4 mb-4">
                    <div className="col-12">
                      <div className="bg-light rounded p-3">
                        <div className="d-flex align-items-center mb-2">
                          <i className="bi bi-person text-primary me-2"></i>
                          <small className="text-muted text-uppercase fw-bold">Username</small>
                        </div>
                        <h6 className="mb-0 fw-bold">{currentUser.username}</h6>
                      </div>
                    </div>

                    <div className="col-12">
                      <div className="bg-light rounded p-3">
                        <div className="d-flex align-items-center mb-2">
                          <i className="bi bi-envelope text-primary me-2"></i>
                          <small className="text-muted text-uppercase fw-bold">Email Address</small>
                        </div>
                        <h6 className="mb-0 fw-bold">{currentUser.email}</h6>
                      </div>
                    </div>
                  </div>

                  <hr className="my-4" />

                  <div className="d-flex gap-2">
                    <button 
                      onClick={() => setEditMode(true)} 
                      className="btn btn-primary btn-lg flex-fill"
                    >
                      <i className="bi bi-pencil-square me-2"></i>
                      Edit Profile
                    </button>
                    <button 
                      onClick={handleLogout} 
                      className="btn btn-outline-danger btn-lg"
                    >
                      <i className="bi bi-box-arrow-right me-2"></i>
                      Logout
                    </button>
                  </div>

                  <div className="text-center mt-4">
                    <button 
                      onClick={() => navigate('/order-history')} 
                      className="btn btn-outline-primary"
                    >
                      <i className="bi bi-clock-history me-2"></i>
                      View Order History
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>

          <div className="text-center mt-4">
            <small className="text-muted">
              <i className="bi bi-shield-check me-1"></i>
              Your information is secure and encrypted
            </small>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;