export interface Product {
  id: string;
  name: string;
  price: number;
  category: string;
  rating: number;
  stock: number;
  image?: string; // Optional image field
}

export interface User {
  id: string;
  username: string;
  password?: string; // For simple auth, in a real app this would be hashed
  email: string;
}

export interface CartItem {
  product: Product;
  quantity: number;
}

export interface Order {
  id: string;
  userId: string;
  items: CartItem[];
  total: number;
  orderDate: string;
}
