import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { type RootState } from '../store';
import { updateQuantity, removeFromCart } from '../store/cartSlice';
import type { CartItem } from '../types';

const CartPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const cartItems = useSelector((state: RootState) => state.cart.items);
  const currentUser = useSelector((state: RootState) => state.user.currentUser);

  useEffect(() => {
    if (!currentUser) {
      navigate('/login');
    }
  }, [currentUser, navigate]);

  const handleRemoveItem = (productId: string) => {
    dispatch(removeFromCart(productId));
  };

  const handleUpdateQuantity = (productId: string, newQuantity: number) => {
    if (newQuantity < 1) return;
    dispatch(updateQuantity({ productId, quantity: newQuantity }));
  };

  const total = cartItems.reduce((sum, item) => sum + item.product.price * item.quantity, 0);

  return (
    <div className="container my-4">
      <div className="row">
        <div className="col-12">
          <h2 className="mb-4">
            <i className="bi bi-cart3 me-2"></i>
            Your Cart
            {cartItems.length > 0 && (
              <span className="badge bg-primary ms-2">{cartItems.length}</span>
            )}
          </h2>
        </div>
      </div>

      {cartItems.length === 0 ? (
        <div className="row">
          <div className="col-12">
            <div className="card text-center py-5">
              <div className="card-body">
                <i className="bi bi-cart-x display-1 text-muted mb-3"></i>
                <h4 className="card-title">Your cart is empty</h4>
                <p className="card-text text-muted">
                  Looks like you haven't added anything to your cart yet.
                </p>
                <button 
                  onClick={() => navigate('/')}
                  className="btn btn-primary btn-lg"
                >
                  Start Shopping
                </button>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="row">
          <div className="col-lg-8">
            <div className="card">
              <div className="card-header">
                <h5 className="mb-0">Cart Items</h5>
              </div>
              <div className="card-body p-0">
                {cartItems.map((item, index) => (
                  <div key={item.product.id} className={`p-4 ${index !== cartItems.length - 1 ? 'border-bottom' : ''}`}>
                    <div className="row align-items-center">
                      <div className="col-md-6">
                        <h5 className="mb-1">{item.product.name}</h5>
                        <p className="text-muted mb-2">
                          <span className="fw-semibold text-primary">
                            ${item.product.price.toFixed(2)}
                          </span> each
                        </p>
                        <small className="text-muted">
                          Stock available: {item.product.stock}
                        </small>
                      </div>
                      
                      <div className="col-md-3">
                        <label className="form-label small text-muted">Quantity</label>
                        <div className="input-group input-group-sm">
                          <button
                            onClick={() => handleUpdateQuantity(item.product.id, item.quantity - 1)}
                            disabled={item.quantity === 1}
                            className="btn btn-outline-secondary"
                            type="button"
                          >
                            -
                          </button>
                          <input
                            type="number"
                            value={item.quantity}
                            onChange={(e) =>
                              handleUpdateQuantity(item.product.id, parseInt(e.target.value) || 1)
                            }
                            min="1"
                            max={item.product.stock}
                            className="form-control text-center"
                          />
                          <button
                            onClick={() => handleUpdateQuantity(item.product.id, item.quantity + 1)}
                            disabled={item.quantity >= item.product.stock}
                            className="btn btn-outline-secondary"
                            type="button"
                          >
                            +
                          </button>
                        </div>
                      </div>
                      
                      <div className="col-md-2 text-center">
                        <p className="mb-1">
                          <strong className="text-success">
                            ${(item.product.price * item.quantity).toFixed(2)}
                          </strong>
                        </p>
                        <small className="text-muted">Subtotal</small>
                      </div>
                      
                      <div className="col-md-1 text-center">
                        <button
                          onClick={() => handleRemoveItem(item.product.id)}
                          className="btn btn-outline-danger btn-sm"
                          title="Remove item"
                        >
                          🗑️
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
          
          <div className="col-lg-4 mt-4 mt-lg-0">
            <div className="card sticky-top" style={{ top: '20px' }}>
              <div className="card-header">
                <h5 className="mb-0">Order Summary</h5>
              </div>
              <div className="card-body">
                <div className="d-flex justify-content-between mb-3">
                  <span>Items ({cartItems.length}):</span>
                  <span>${total.toFixed(2)}</span>
                </div>
                <div className="d-flex justify-content-between mb-3">
                  <span>Shipping:</span>
                  <span className="text-success">FREE</span>
                </div>
                <hr />
                <div className="d-flex justify-content-between mb-4">
                  <strong>Total:</strong>
                  <strong className="text-primary fs-4">
                    ${total.toFixed(2)}
                  </strong>
                </div>
                <button 
                  onClick={() => navigate('/checkout')} 
                  className="btn btn-primary btn-lg w-100 mb-2"
                >
                  <i className="bi bi-credit-card me-2"></i>
                  Proceed to Checkout
                </button>
                <button 
                  onClick={() => navigate('/')}
                  className="btn btn-outline-secondary w-100"
                >
                  Continue Shopping
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CartPage;
