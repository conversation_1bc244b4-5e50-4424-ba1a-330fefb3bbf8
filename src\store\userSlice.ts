import { createSlice, type PayloadAction } from '@reduxjs/toolkit';
import { type User } from '../types';
import { act } from 'react';

export interface UserState {
  currentUser: User | null;
  users: User[];
}

const initialState: UserState = {
  currentUser: null,
  users: [],
};

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setCurrentUser: (state, action: PayloadAction<User | null>) => {
      state.currentUser = action.payload;
    },
    addUser: (state, action: PayloadAction<User>) => {
      state.users.push(action.payload);
    },
    updateUser: (state, action: PayloadAction<Partial<User>>) => {
      if (state.currentUser) {
        state.currentUser = { ...state.currentUser, ...action.payload };
        state.users = state.users.map(user =>
          user.id === state.currentUser?.id ? state.currentUser : user
        );
      }
    },
    clearCurrentUser: (state) => {
      state.currentUser = null;
    },
    setUsers: (state, action: PayloadAction<User[]>) => {
      state.users = action.payload;
    },
    registerUser: (state, action: PayloadAction<User>) => {
      state.users.push(action.payload);
      state.currentUser = action.payload;
    },
    loginUser: (state, action: PayloadAction<User>) => {
      state.currentUser = action.payload;
    },
    logoutUser: (state) => {
      state.currentUser = null;
    },
  },
});

export const { setCurrentUser, addUser, updateUser, clearCurrentUser, setUsers, registerUser, loginUser,  logoutUser } = userSlice.actions;
export default userSlice.reducer;
