import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { type RootState } from '../store';
import { addToCart } from '../store/cartSlice';
import type { Product, CartItem } from '../types';

const ProductDetailsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const products = useSelector((state: RootState) => state.product.products);
  const cartItems = useSelector((state: RootState) => state.cart.items);
  const currentUser = useSelector((state: RootState) => state.user.currentUser);

  const product = products.find((p) => p.id === id) || null;
  const [quantity, setQuantity] = useState(1);
  const [message, setMessage] = useState('');

  useEffect(() => {
    if (!currentUser) {
      navigate('/login');
      return;
    }
    if (!product) {
      navigate('/'); // Redirect if product not found in Redux store
    }
  }, [currentUser, product, navigate]);

  const handleAddToCart = () => {
    if (!product) return;

    if (product.stock === 0) {
      setMessage('This product is out of stock.');
      return;
    }

    if (quantity > product.stock) {
      setMessage(`Cannot add ${quantity} items. Only ${product.stock} in stock.`);
      return;
    }

    const existingItem = cartItems.find((item) => item.product.id === product.id);

    if (existingItem) {
      const newQuantity = existingItem.quantity + quantity;

      if (newQuantity > product.stock) {
        setMessage(`Adding ${quantity} would exceed stock. Current in cart: ${existingItem.quantity}, Available: ${product.stock}`);
        return;
      }

      dispatch(addToCart({ product, quantity: newQuantity }));
    } else {
      const newItem: CartItem = {
        product: { ...product },
        quantity: quantity,
      };
      dispatch(addToCart(newItem));
    }
    setMessage(`${quantity} x ${product.name} added to cart!`);
  };

  if (!product) {
    return (
      <div className="container my-5">
        <div className="row justify-content-center">
          <div className="col-md-6">
            <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '200px' }}>
              <div className="spinner-border text-primary" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
              <span className="ms-3">Loading product details...</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container my-4">
      <nav aria-label="breadcrumb" className="mb-4">
        <ol className="breadcrumb">
          <li className="breadcrumb-item">
            <button 
              onClick={() => navigate(-1)} 
              className="btn btn-link p-0 text-decoration-none"
            >
              ← Back
            </button>
          </li>
        </ol>
      </nav>

      <div className="row">
        <div className="col-lg-6 mb-4">
          <div className="card">
            {product.image ? (
              <img 
                src={product.image} 
                alt={product.name} 
                className="card-img-top h-100 object-fit-contain"
              />
            ) : (
              <div 
                className="card-img-top d-flex align-items-center justify-content-center bg-light text-muted"
                style={{ height: '400px' }}
              >
                <span>No Image Available</span>
              </div>
            )}
          </div>
        </div>

        <div className="col-lg-6">
          <div className="card h-100">
            <div className="card-body">
              <h1 className="card-title h2 mb-3">{product.name}</h1>
              
              <div className="mb-4">
                <h3 className="text-primary fw-bold mb-3">
                  ${product.price.toFixed(2)}
                </h3>
              </div>

              <div className="row mb-3">
                <div className="col-sm-6 mb-2">
                  <strong>Category:</strong>
                  <span className="badge bg-secondary ms-2">{product.category}</span>
                </div>
                <div className="col-sm-6 mb-2">
                  <strong>Rating:</strong>
                  <div className="ms-2 d-inline">
                    <span className="text-warning">
                      {'★'.repeat(Math.floor(product.rating))}
                      {'☆'.repeat(5 - Math.floor(product.rating))}
                    </span>
                    <small className="text-muted ms-1">({product.rating}/5)</small>
                  </div>
                </div>
              </div>

              <div className="mb-4">
                <strong>Stock Status:</strong>
                <span className={`badge ms-2 ${product.stock > 0 ? 'bg-success' : 'bg-danger'}`}>
                  {product.stock > 0 ? `${product.stock} Available` : 'Out of Stock'}
                </span>
              </div>

              {message && (
                <div className={`alert ${message.includes('added to cart') ? 'alert-success' : 'alert-warning'} alert-dismissible fade show`} role="alert">
                  {message}
                  <button 
                    type="button" 
                    className="btn-close" 
                    onClick={() => setMessage('')}
                    aria-label="Close"
                  ></button>
                </div>
              )}

              {product.stock > 0 && (
                <div className="card bg-light">
                  <div className="card-body">
                    <h5 className="card-title">Add to Cart</h5>
                    <div className="row g-3 align-items-end">
                      <div className="col-md-4">
                        <label htmlFor="quantity" className="form-label">
                          Quantity:
                        </label>
                        <input
                          type="number"
                          id="quantity"
                          min="1"
                          max={product.stock}
                          value={quantity}
                          onChange={(e) => setQuantity(Math.max(1, parseInt(e.target.value) || 1))}
                          className="form-control"
                        />
                      </div>
                      <div className="col-md-8">
                        <button 
                          onClick={handleAddToCart} 
                          className="btn btn-primary btn-lg w-100"
                        >
                          <i className="bi bi-cart-plus me-2"></i>
                          Add to Cart
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {product.stock === 0 && (
                <div className="card bg-light">
                  <div className="card-body text-center">
                    <h5 className="text-muted">This product is currently out of stock</h5>
                    <p className="mb-0">Please check back later or contact us for availability.</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetailsPage;
