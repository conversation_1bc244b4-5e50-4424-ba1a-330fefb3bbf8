import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useDispatch, useSelector } from 'react-redux';
import { registerUser } from '../store/userSlice';
import { type User } from '../types';
import { type RootState } from '../store';

const RegisterPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const users: User[] = useSelector((state: RootState) => state.user.users);

  const validationSchema = Yup.object({
    username: Yup.string().required('Username is required'),
    email: Yup.string().email('Invalid email format').required('Email is required'),
    password: Yup.string().min(6, 'Password must be at least 6 characters').required('Password is required'),
  });

  const formik = useFormik({
    initialValues: {
      username: '',
      email: '',
      password: '',
    },
    validationSchema: validationSchema,
    onSubmit: (values, { setErrors }) => {
      const { username, email, password } = values;

      if (users.some((user: User) => user.username === username)) {
        setErrors({ username: 'Username already exists' });
        return;
      }
      if (users.some((user: User) => user.email === email)) {
        setErrors({ email: 'Email already registered' });
        return;
      }

      const newUser: User = {
        id: Date.now().toString(), // Simple unique ID
        username,
        email,
        password, // In a real app, hash this password
      };

      dispatch(registerUser(newUser));
      navigate('/'); // Navigate to home or dashboard after successful registration
    },
  });

  return (
    <div className="container">
      <div className="row justify-content-center">
        <div className="col-md-6 col-lg-4">
          <div className="card shadow mt-5">
            <div className="card-body">
              <h2 className="card-title text-center mb-4">Register</h2>
              <form onSubmit={formik.handleSubmit}>
                {formik.errors.username && formik.touched.username && (
                  <div className="alert alert-danger" role="alert">
                    {formik.errors.username}
                  </div>
                )}
                {formik.errors.email && formik.touched.email && (
                  <div className="alert alert-danger" role="alert">
                    {formik.errors.email}
                  </div>
                )}
                {formik.errors.password && formik.touched.password && (
                  <div className="alert alert-danger" role="alert">
                    {formik.errors.password}
                  </div>
                )}
                <div className="mb-3">
                  <label htmlFor="username" className="form-label">
                    Username:
                  </label>
                  <input
                    type="text"
                    className="form-control"
                    id="username"
                    {...formik.getFieldProps('username')}
                  />
                </div>
                <div className="mb-3">
                  <label htmlFor="email" className="form-label">
                    Email:
                  </label>
                  <input
                    type="email"
                    className="form-control"
                    id="email"
                    {...formik.getFieldProps('email')}
                  />
                </div>
                <div className="mb-3">
                  <label htmlFor="password" className="form-label">
                    Password:
                  </label>
                  <input
                    type="password"
                    className="form-control"
                    id="password"
                    {...formik.getFieldProps('password')}
                  />
                </div>
                <div className="d-grid">
                  <button type="submit" className="btn btn-primary">
                    Register
                  </button>
                </div>
              </form>
              <p className="text-center mt-3">
                Already have an account?{' '}
                <a href="/login" className="text-decoration-none">
                  Login here
                </a>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RegisterPage;
