import { createSlice, type PayloadAction } from '@reduxjs/toolkit';
import { type Order } from '../types';

export interface OrderState {
  orders: Order[];
}

const initialState: OrderState = {
  orders: [],
};

const orderSlice = createSlice({
  name: 'order',
  initialState,
  reducers: {
    placeOrder: (state, action: PayloadAction<Order>) => {
      state.orders.push(action.payload);
    },
    setOrders: (state, action: PayloadAction<Order[]>) => {
      state.orders = action.payload;
    },
  },
});

export const { placeOrder, setOrders } = orderSlice.actions;
export default orderSlice.reducer;
