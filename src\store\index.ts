import { combineReducers, configureStore } from '@reduxjs/toolkit';
import userReducer, { type UserState } from './userSlice';
import productReducer, { type ProductState } from './productSlice';
import cartReducer, { type CartState } from './cartSlice';
import orderReducer, { type OrderState } from './orderSlice';
import { loadState, saveState } from '../utils/localStorage';

const rootReducer = combineReducers({
  user: userReducer,
  product: productReducer,
  cart: cartReducer,
  order: orderReducer,
});

export const setupStore = (preloadedState?: Partial<RootState>) => {
  return configureStore({
    reducer: rootReducer,
    preloadedState,
  });
};

export type RootState = {
  user: UserState;
  product: ProductState;
  cart: CartState;
  order: OrderState;
};
export type AppStore = ReturnType<typeof setupStore>;
export type AppDispatch = AppStore['dispatch'];

const preloadedState = loadState();
const appStore = setupStore(preloadedState);

appStore.subscribe(() => {
  saveState(appStore.getState());
});

export default appStore;
